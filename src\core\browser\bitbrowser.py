"""
比特浏览器管理器实现

基于现有的bit_api.py实现比特浏览器的管理功能，
提供统一的浏览器管理接口。
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any
import requests
from datetime import datetime

from .base import (
    BrowserManager, BrowserInstance, BrowserStatus, BrowserType,
    BrowserConfig, BrowserAPIError, BrowserOpenError, BrowserCloseError
)


class BitBrowserManager(BrowserManager):
    """比特浏览器管理器"""
    
    def __init__(self, config: BrowserConfig):
        """
        初始化比特浏览器管理器
        
        Args:
            config: 浏览器配置
        """
        super().__init__(config)
        self.api_url = config.api_url
        self.headers = {'Content-Type': 'application/json'}
        self.timeout = config.timeout
    
    async def check_api_server(self) -> bool:
        """
        检查比特浏览器API服务是否可用

        Returns:
            bool: 服务是否可用
        """
        try:
            if not self.api_url:
                self.logger.error("API URL未配置")
                return False

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(f"{self.api_url}/browser/list", timeout=5)
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"检查API服务失败: {str(e)}")
            return False
    
    async def open_browser(self, profile_id: str, **kwargs) -> Optional[BrowserInstance]:
        """
        打开比特浏览器窗口
        
        Args:
            profile_id: 比特浏览器的窗口ID
            **kwargs: 额外参数
                - window_width: 窗口宽度
                - window_height: 窗口高度
                - useragent: 用户代理
                - remark: 备注
        
        Returns:
            BrowserInstance: 浏览器实例，失败返回None
        """
        try:
            # 检查API服务
            if not await self.check_api_server():
                raise BrowserAPIError("比特浏览器API服务不可用")
            
            self.logger.info(f"正在打开比特浏览器窗口: {profile_id}")
            
            # 准备打开窗口的请求数据
            json_data = {
                "id": profile_id,
                "useragent": kwargs.get("useragent", ""),
                "remark": kwargs.get("remark", "")
            }
            
            # 发送打开窗口请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    f"{self.api_url}/browser/open",
                    data=json.dumps(json_data),
                    headers=self.headers,
                    timeout=self.timeout
                )
            )
            
            if response.status_code != 200:
                raise BrowserOpenError(f"打开窗口失败: HTTP {response.status_code}, {response.text}")
            
            result = response.json()
            self.logger.info(f"窗口打开响应: {result}")
            
            # 等待窗口完全打开
            await asyncio.sleep(2)
            
            # 创建浏览器实例
            instance = BrowserInstance(
                profile_id=profile_id,
                browser_type=BrowserType.BITBROWSER,
                window_id=profile_id,
                status=BrowserStatus.OPEN,
                ws_endpoint=result.get('data', {}).get('ws', ''),
                created_at=datetime.now().isoformat()
            )
            
            # 设置窗口尺寸（如果提供了参数）
            window_width = kwargs.get("window_width")
            window_height = kwargs.get("window_height")
            if window_width and window_height:
                await self._set_window_size(window_width, window_height)
            
            # 添加到实例管理
            self.add_instance(instance)
            
            return instance
            
        except Exception as e:
            error_msg = f"打开比特浏览器窗口失败 {profile_id}: {str(e)}"
            self.logger.error(error_msg)
            
            # 创建错误实例
            error_instance = BrowserInstance(
                profile_id=profile_id,
                browser_type=BrowserType.BITBROWSER,
                window_id=profile_id,
                status=BrowserStatus.ERROR,
                error_message=str(e),
                created_at=datetime.now().isoformat()
            )
            self.add_instance(error_instance)
            
            return None
    
    async def close_browser(self, instance: BrowserInstance) -> bool:
        """
        关闭比特浏览器窗口
        
        Args:
            instance: 浏览器实例
            
        Returns:
            bool: 是否成功关闭
        """
        try:
            self.logger.info(f"正在关闭比特浏览器窗口: {instance.profile_id}")
            
            # 更新状态为关闭中
            instance.status = BrowserStatus.CLOSING
            
            json_data = {'id': instance.window_id}
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    f"{self.api_url}/browser/close",
                    data=json.dumps(json_data),
                    headers=self.headers,
                    timeout=self.timeout
                )
            )
            
            self.logger.info(f"关闭窗口响应: {response.text}")
            
            # 更新状态为已关闭
            instance.status = BrowserStatus.CLOSED
            
            # 从实例管理中移除
            self.remove_instance(instance.profile_id)
            
            return True
            
        except Exception as e:
            error_msg = f"关闭比特浏览器窗口失败 {instance.profile_id}: {str(e)}"
            self.logger.error(error_msg)
            instance.status = BrowserStatus.ERROR
            instance.error_message = str(e)
            return False
    
    async def get_browser_status(self, instance: BrowserInstance) -> BrowserStatus:
        """
        获取比特浏览器状态
        
        Args:
            instance: 浏览器实例
            
        Returns:
            BrowserStatus: 浏览器状态
        """
        # 比特浏览器API可能没有直接的状态查询接口
        # 这里返回实例中记录的状态
        return instance.status
    
    async def get_playwright_endpoint(self, instance: BrowserInstance) -> Optional[str]:
        """
        获取Playwright连接端点
        
        Args:
            instance: 浏览器实例
            
        Returns:
            str: WebSocket端点URL，失败返回None
        """
        if instance.status != BrowserStatus.OPEN:
            self.logger.warning(f"浏览器实例未打开: {instance.profile_id}")
            return None
        
        return instance.ws_endpoint
    
    async def _set_window_size(self, width: int, height: int):
        """
        设置窗口尺寸
        
        Args:
            width: 窗口宽度
            height: 窗口高度
        """
        try:
            size_data = {
                "type": "box",
                "startX": 0,
                "startY": 0,
                "width": width,
                "height": height,
                "col": 4,
                "spaceX": 20,
                "spaceY": 20,
                "offsetX": 0,
                "offsetY": 0,
                "orderBy": "asc",
                "seqlist": []
            }
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    f"{self.api_url}/windowbounds",
                    data=json.dumps(size_data),
                    headers=self.headers,
                    timeout=self.timeout
                )
            )
            
            if response.status_code != 200:
                self.logger.warning(f"设置窗口尺寸失败: {response.status_code}, {response.text}")
            else:
                self.logger.info("窗口尺寸设置成功")
                
        except Exception as e:
            self.logger.error(f"设置窗口尺寸时发生错误: {str(e)}")
    
    async def arrange_windows(self, width: int, height: int) -> bool:
        """
        自动排列所有窗口
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            
        Returns:
            bool: 是否成功
        """
        try:
            json_data = {
                "type": "box",
                "startX": 0,
                "startY": 0,
                "width": width,
                "height": height,
                "col": 4,
                "spaceX": 20,
                "spaceY": 20,
                "offsetX": 0,
                "offsetY": 0,
                "orderBy": "asc",
                "seqlist": []
            }
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    f"{self.api_url}/windowbounds",
                    data=json.dumps(json_data),
                    headers=self.headers,
                    timeout=self.timeout
                )
            )
            
            if response.status_code == 200:
                self.logger.info("窗口排列成功")
                return True
            else:
                self.logger.error(f"窗口排列失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"窗口排列时发生错误: {str(e)}")
            return False
