"""
比特浏览器管理器实现

基于现有的bit_api.py实现比特浏览器的管理功能，
提供统一的浏览器管理接口。
"""

import asyncio
import json
import time
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径以导入bit_api
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

try:
    import bit_api
except ImportError:
    # 如果导入失败，尝试相对路径
    bit_api = None

from .base import (
    BrowserManager, BrowserInstance, BrowserStatus, BrowserType,
    BrowserConfig, BrowserAPIError, BrowserOpenError, BrowserCloseError
)


class BitBrowserManager(BrowserManager):
    """比特浏览器管理器"""

    def __init__(self, config: BrowserConfig):
        """
        初始化比特浏览器管理器

        Args:
            config: 浏览器配置
        """
        super().__init__(config)
        self.api_url = config.api_url
        self.timeout = config.timeout

        # 如果bit_api导入失败，记录错误
        if bit_api is None:
            self.logger.error("无法导入bit_api模块，请检查bit_api.py文件是否存在")
        else:
            # 更新bit_api中的URL配置
            bit_api.url = self.api_url

    async def check_api_server(self) -> bool:
        """
        检查比特浏览器API服务是否可用

        Returns:
            bool: 服务是否可用
        """
        try:
            if bit_api is None:
                self.logger.error("bit_api模块未导入")
                return False

            # 使用bit_api中的函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, bit_api.check_api_server)
            return result
        except Exception as e:
            self.logger.error(f"检查API服务失败: {str(e)}")
            return False
    
    async def open_browser(self, profile_id: str, **kwargs) -> Optional[BrowserInstance]:
        """
        打开比特浏览器窗口

        Args:
            profile_id: 比特浏览器的窗口ID
            **kwargs: 额外参数
                - window_width: 窗口宽度
                - window_height: 窗口高度
                - useragent: 用户代理
                - remark: 备注

        Returns:
            BrowserInstance: 浏览器实例，失败返回None
        """
        try:
            if bit_api is None:
                raise BrowserAPIError("bit_api模块未导入")

            # 检查API服务
            if not await self.check_api_server():
                raise BrowserAPIError("比特浏览器API服务不可用")

            self.logger.info(f"正在打开比特浏览器窗口: {profile_id}")

            # 使用bit_api中的openBrowser函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, bit_api.openBrowser, profile_id)

            if result is None:
                raise BrowserOpenError("bit_api.openBrowser返回None")

            self.logger.info(f"窗口打开响应: {result}")

            # 创建浏览器实例
            instance = BrowserInstance(
                profile_id=profile_id,
                browser_type=BrowserType.BITBROWSER,
                window_id=profile_id,
                status=BrowserStatus.OPEN,
                ws_endpoint=result.get('data', {}).get('ws', '') if isinstance(result, dict) else '',
                created_at=datetime.now().isoformat()
            )

            # 添加到实例管理
            self.add_instance(instance)

            return instance

        except Exception as e:
            error_msg = f"打开比特浏览器窗口失败 {profile_id}: {str(e)}"
            self.logger.error(error_msg)

            # 创建错误实例
            error_instance = BrowserInstance(
                profile_id=profile_id,
                browser_type=BrowserType.BITBROWSER,
                window_id=profile_id,
                status=BrowserStatus.ERROR,
                error_message=str(e),
                created_at=datetime.now().isoformat()
            )
            self.add_instance(error_instance)

            return None
    
    async def close_browser(self, instance: BrowserInstance) -> bool:
        """
        关闭比特浏览器窗口

        Args:
            instance: 浏览器实例

        Returns:
            bool: 是否成功关闭
        """
        try:
            if bit_api is None:
                self.logger.error("bit_api模块未导入")
                return False

            self.logger.info(f"正在关闭比特浏览器窗口: {instance.profile_id}")

            # 更新状态为关闭中
            instance.status = BrowserStatus.CLOSING

            # 使用bit_api中的closeBrowser函数
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, bit_api.closeBrowser, instance.window_id)

            # 更新状态为已关闭
            instance.status = BrowserStatus.CLOSED

            # 从实例管理中移除
            self.remove_instance(instance.profile_id)

            return True

        except Exception as e:
            error_msg = f"关闭比特浏览器窗口失败 {instance.profile_id}: {str(e)}"
            self.logger.error(error_msg)
            instance.status = BrowserStatus.ERROR
            instance.error_message = str(e)
            return False
    
    async def get_browser_status(self, instance: BrowserInstance) -> BrowserStatus:
        """
        获取比特浏览器状态
        
        Args:
            instance: 浏览器实例
            
        Returns:
            BrowserStatus: 浏览器状态
        """
        # 比特浏览器API可能没有直接的状态查询接口
        # 这里返回实例中记录的状态
        return instance.status
    
    async def get_playwright_endpoint(self, instance: BrowserInstance) -> Optional[str]:
        """
        获取Playwright连接端点
        
        Args:
            instance: 浏览器实例
            
        Returns:
            str: WebSocket端点URL，失败返回None
        """
        if instance.status != BrowserStatus.OPEN:
            self.logger.warning(f"浏览器实例未打开: {instance.profile_id}")
            return None
        
        return instance.ws_endpoint
    
    async def arrange_windows(self, width: int, height: int) -> bool:
        """
        自动排列所有窗口

        Args:
            width: 窗口宽度
            height: 窗口高度

        Returns:
            bool: 是否成功
        """
        try:
            if bit_api is None:
                self.logger.error("bit_api模块未导入")
                return False

            # 使用bit_api中的arrangeWindows函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, bit_api.arrangeWindows)

            if result is not None:
                self.logger.info("窗口排列成功")
                return True
            else:
                self.logger.error("窗口排列失败")
                return False

        except Exception as e:
            self.logger.error(f"窗口排列时发生错误: {str(e)}")
            return False
