"""
任务执行API

提供任务管理功能：
- 启动数据抓取任务
- 获取任务状态
- 停止任务
- 获取任务历史
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import sys
from pathlib import Path
import asyncio
from datetime import datetime
import uuid

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.services.profile_manager import ProfileManager
from src.core.tasks.youtube_task_manager import YouTubeTaskManager
from src.backend.websocket_manager import websocket_manager

router = APIRouter()

# 任务数据模型
class TaskStart(BaseModel):
    profile_ids: List[str]
    task_type: str = "youtube_scraping"

class TaskResponse(BaseModel):
    task_id: str
    status: str
    profile_ids: List[str]
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: int = 0
    message: str = ""
    results: Optional[Dict[str, Any]] = None

# 全局任务管理器
task_manager = YouTubeTaskManager({
    'max_concurrent_tasks': 3,
    'task_timeout': 1800,
    'retry_count': 3,
    'retry_delay': 60
})
active_tasks: Dict[str, Dict] = {}

@router.post("/tasks/start", response_model=TaskResponse)
async def start_task(task_data: TaskStart, background_tasks: BackgroundTasks):
    """启动数据抓取任务"""
    try:
        # 验证Profile存在
        profile_manager = ProfileManager()
        profiles = []
        for profile_id in task_data.profile_ids:
            profile = profile_manager.get_profile(profile_id)
            if not profile:
                raise HTTPException(status_code=404, detail=f"Profile {profile_id} 不存在")
            if not profile.is_active:
                raise HTTPException(status_code=400, detail=f"Profile {profile.name} 未启用")
            profiles.append(profile)
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 记录任务信息
        task_info = {
            "task_id": task_id,
            "status": "pending",
            "profile_ids": task_data.profile_ids,
            "profiles": profiles,
            "created_at": datetime.now().isoformat(),
            "started_at": None,
            "completed_at": None,
            "progress": 0,
            "message": "任务已创建，等待执行",
            "results": None
        }
        active_tasks[task_id] = task_info
        
        # 在后台执行任务
        background_tasks.add_task(execute_scraping_task, task_id, profiles)
        
        # 发送任务状态通知
        await websocket_manager.send_task_status(task_id, "pending", {
            "profile_count": len(profiles),
            "profile_names": [p.name for p in profiles]
        })
        
        return TaskResponse(
            task_id=task_id,
            status="pending",
            profile_ids=task_data.profile_ids,
            created_at=task_info["created_at"],
            progress=0,
            message="任务已创建，等待执行"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_info = active_tasks[task_id]
    return TaskResponse(
        task_id=task_id,
        status=task_info["status"],
        profile_ids=task_info["profile_ids"],
        created_at=task_info["created_at"],
        started_at=task_info["started_at"],
        completed_at=task_info["completed_at"],
        progress=task_info["progress"],
        message=task_info["message"],
        results=task_info["results"]
    )

@router.get("/tasks", response_model=List[TaskResponse])
async def get_all_tasks():
    """获取所有任务"""
    return [
        TaskResponse(
            task_id=task_id,
            status=task_info["status"],
            profile_ids=task_info["profile_ids"],
            created_at=task_info["created_at"],
            started_at=task_info["started_at"],
            completed_at=task_info["completed_at"],
            progress=task_info["progress"],
            message=task_info["message"],
            results=task_info["results"]
        )
        for task_id, task_info in active_tasks.items()
    ]

@router.post("/tasks/{task_id}/stop")
async def stop_task(task_id: str):
    """停止任务"""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_info = active_tasks[task_id]
    if task_info["status"] in ["completed", "failed", "stopped"]:
        raise HTTPException(status_code=400, detail="任务已结束，无法停止")
    
    # 更新任务状态
    task_info["status"] = "stopping"
    task_info["message"] = "正在停止任务..."
    
    # 发送停止通知
    await websocket_manager.send_task_status(task_id, "stopping")
    
    # TODO: 实现实际的任务停止逻辑
    
    return {"message": "任务停止请求已发送"}

async def execute_scraping_task(task_id: str, profiles: List):
    """执行数据抓取任务"""
    task_info = active_tasks[task_id]
    
    try:
        # 更新任务状态为运行中
        task_info["status"] = "running"
        task_info["started_at"] = datetime.now().isoformat()
        task_info["message"] = "任务正在执行中..."
        
        await websocket_manager.send_task_status(task_id, "running")
        
        # 执行数据抓取
        results = {}
        total_profiles = len(profiles)
        
        for i, profile in enumerate(profiles):
            if task_info["status"] == "stopping":
                break
            
            # 更新进度
            progress = int((i / total_profiles) * 100)
            task_info["progress"] = progress
            task_info["message"] = f"正在处理 {profile.name}..."
            
            await websocket_manager.send_task_progress(task_id, progress, f"正在处理 {profile.name}...")
            
            try:
                # 执行真正的YouTube数据抓取
                await websocket_manager.send_task_progress(task_id, progress, f"正在启动浏览器 - {profile.name}...")

                # 使用真正的YouTube抓取器
                from src.core.scrapers.youtube_scraper import YouTubeScraper
                from src.core.browser.factory import BrowserManagerFactory

                # 创建浏览器管理器配置
                browser_config = {
                    'window_id': profile.browser_window_id,
                    'headless': False,  # 不使用无头模式
                    'debug': True,
                    'timeout': 30,
                    'retry_count': 3,
                    'retry_delay': 2
                }

                # 根据浏览器类型设置API URL
                if profile.browser_type == 'bitbrowser':
                    browser_config['api_url'] = 'http://127.0.0.1:54345'  # 比特浏览器默认API地址
                elif profile.browser_type == 'adspower':
                    browser_config['api_url'] = 'http://127.0.0.1:50325'  # ADS Power默认API地址

                browser_manager = BrowserManagerFactory.create_manager(
                    profile.browser_type,
                    browser_config
                )

                if not browser_manager:
                    raise Exception(f"无法创建{profile.browser_type}浏览器管理器")

                await websocket_manager.send_task_progress(task_id, progress + 10, f"正在连接浏览器 - {profile.name}...")

                # 检查浏览器API服务
                if not await browser_manager.check_api_server():
                    raise Exception(f"{profile.browser_type}浏览器API服务不可用，请确保浏览器已启动")

                # 创建YouTube抓取器配置
                scraper_config = {
                    'page_wait_timeout': 30000,
                    'element_wait_timeout': 10000,
                    'scroll_delay': 2000,
                    'max_retries': 3
                }
                scraper = YouTubeScraper(browser_manager, scraper_config)

                await websocket_manager.send_task_progress(task_id, progress + 20, f"开始抓取数据 - {profile.name}...")

                # 执行数据抓取
                video_data_list = await scraper.scrape_profile_data(profile)

                await websocket_manager.send_task_progress(task_id, progress + 70, f"数据抓取完成 - {profile.name}...")

                # 处理抓取结果
                if video_data_list and len(video_data_list) > 0:
                    # 转换为字典格式
                    videos = [video.to_dict() for video in video_data_list]

                    profile_result = {
                        "videos_scraped": len(videos),
                        "total_views": sum(video.get('views', 0) for video in videos),
                        "total_likes": sum(video.get('likes', 0) for video in videos),
                        "total_comments": sum(video.get('comments', 0) for video in videos),
                        "videos": videos,
                        "scrape_time": datetime.now().isoformat(),
                        "profile_name": profile.name,
                        "youtube_account": profile.youtube_account
                    }

                    results[profile.id] = {
                        "profile_name": profile.name,
                        "status": "success",
                        "data": profile_result,
                        "message": f"成功抓取 {profile_result['videos_scraped']} 个视频数据"
                    }
                else:
                    results[profile.id] = {
                        "profile_name": profile.name,
                        "status": "error",
                        "data": None,
                        "message": "数据抓取失败：未获取到有效数据"
                    }

                # 清理浏览器资源
                try:
                    await browser_manager.close()
                except:
                    pass

            except Exception as e:
                results[profile.id] = {
                    "profile_name": profile.name,
                    "status": "error",
                    "data": None,
                    "message": f"数据抓取失败: {str(e)}"
                }
            
            # 短暂延迟，模拟处理时间
            await asyncio.sleep(0.5)
        
        # 任务完成
        if task_info["status"] == "stopping":
            task_info["status"] = "stopped"
            task_info["message"] = "任务已停止"
        else:
            task_info["status"] = "completed"
            task_info["progress"] = 100
            task_info["message"] = "任务执行完成"
        
        task_info["completed_at"] = datetime.now().isoformat()
        task_info["results"] = results
        
        await websocket_manager.send_task_status(task_id, task_info["status"], {
            "results": results,
            "total_profiles": total_profiles,
            "success_count": len([r for r in results.values() if r["status"] == "success"]),
            "error_count": len([r for r in results.values() if r["status"] == "error"])
        })
        
    except Exception as e:
        # 任务执行失败
        task_info["status"] = "failed"
        task_info["completed_at"] = datetime.now().isoformat()
        task_info["message"] = f"任务执行失败: {str(e)}"
        
        await websocket_manager.send_task_status(task_id, "failed", {
            "error": str(e)
        })
        
        print(f"任务 {task_id} 执行失败: {e}")
