"""
日志工具模块

提供统一的日志配置和管理功能，支持：
- 多级别日志记录
- 文件轮转
- 彩色控制台输出
- 自定义格式化
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

try:
    import colorlog
    HAS_COLORLOG = True
except ImportError:
    HAS_COLORLOG = False


class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def format(self, record):
        # 添加自定义字段
        record.module_name = record.name.split('.')[-1]
        return super().format(record)


def setup_logger(config: Dict[str, Any]) -> logging.Logger:
    """
    设置应用程序日志系统
    
    Args:
        config: 日志配置字典
        
    Returns:
        logging.Logger: 配置好的根日志器
    """
    # 获取配置参数
    log_level = config.get('log_level', 'INFO').upper()
    log_dir = config.get('log_dir', 'logs')
    max_log_files = config.get('max_log_files', 10)
    max_log_size_mb = config.get('max_log_size_mb', 10)
    log_format = config.get('log_format', '[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s')
    date_format = config.get('date_format', '%Y-%m-%d %H:%M:%S')
    
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level, logging.INFO))
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    # 创建文件处理器（带轮转）
    log_file = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_log_size_mb * 1024 * 1024,
        backupCount=max_log_files,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    
    # 文件处理器格式化
    file_formatter = CustomFormatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level, logging.INFO))
    
    # 控制台处理器格式化（支持彩色）
    if HAS_COLORLOG:
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s[%(asctime)s] [%(levelname)s] [%(module_name)s] - %(message)s',
            datefmt=date_format,
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
    else:
        console_formatter = CustomFormatter(
            '[%(asctime)s] [%(levelname)s] [%(module_name)s] - %(message)s',
            date_format
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 记录日志系统启动信息
    root_logger.info(f"日志系统初始化完成 - 级别: {log_level}, 文件: {log_file}")
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器

    Args:
        name: 日志器名称

    Returns:
        logging.Logger: 日志器实例
    """
    logger = logging.getLogger(name)

    # 如果根日志器没有处理器，说明日志系统未初始化，进行基本初始化
    root_logger = logging.getLogger()
    if not root_logger.handlers:
        # 基本的控制台日志配置
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)

        # 设置格式
        formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s')
        console_handler.setFormatter(formatter)

        # 添加到根日志器
        root_logger.addHandler(console_handler)
        root_logger.setLevel(logging.INFO)

        # 避免重复日志
        root_logger.propagate = False

    return logger


class LoggerMixin:
    """日志器混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return logging.getLogger(self.__class__.__name__)


class TaskLogger:
    """任务专用日志器，用于记录任务执行过程"""
    
    def __init__(self, task_id: str, profile_name: str = ""):
        self.task_id = task_id
        self.profile_name = profile_name
        self.logger = logging.getLogger(f"Task.{task_id}")
        self.start_time = datetime.now()
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.profile_name}] {message}")
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.profile_name}] {message}")
    
    def error(self, message: str, exc_info: bool = False):
        """记录错误日志"""
        self.logger.error(f"[{self.profile_name}] {message}", exc_info=exc_info)
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(f"[{self.profile_name}] {message}")
    
    def task_start(self):
        """记录任务开始"""
        self.info(f"任务开始执行 - ID: {self.task_id}")
    
    def task_complete(self, success: bool = True, message: str = ""):
        """记录任务完成"""
        duration = datetime.now() - self.start_time
        status = "成功" if success else "失败"
        self.info(f"任务执行{status} - 耗时: {duration} {message}")
    
    def step(self, step_name: str, message: str = ""):
        """记录任务步骤"""
        self.info(f"步骤: {step_name} {message}")


def setup_task_logger(task_id: str, profile_name: str = "") -> TaskLogger:
    """
    创建任务日志器
    
    Args:
        task_id: 任务ID
        profile_name: Profile名称
        
    Returns:
        TaskLogger: 任务日志器实例
    """
    return TaskLogger(task_id, profile_name)


def cleanup_old_logs(log_dir: str, days_to_keep: int = 30):
    """
    清理旧的日志文件
    
    Args:
        log_dir: 日志目录
        days_to_keep: 保留天数
    """
    try:
        log_path = Path(log_dir)
        if not log_path.exists():
            return
        
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
        
        for log_file in log_path.glob('*.log*'):
            if log_file.stat().st_mtime < cutoff_time:
                log_file.unlink()
                logging.getLogger(__name__).info(f"删除旧日志文件: {log_file}")
                
    except Exception as e:
        logging.getLogger(__name__).error(f"清理旧日志文件失败: {str(e)}")


# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}


def set_log_level(level: str):
    """
    动态设置日志级别
    
    Args:
        level: 日志级别字符串
    """
    if level.upper() in LOG_LEVELS:
        logging.getLogger().setLevel(LOG_LEVELS[level.upper()])
        logging.getLogger(__name__).info(f"日志级别已设置为: {level.upper()}")
    else:
        logging.getLogger(__name__).warning(f"无效的日志级别: {level}")


def get_log_files(log_dir: str) -> list[str]:
    """
    获取日志文件列表
    
    Args:
        log_dir: 日志目录
        
    Returns:
        List[str]: 日志文件路径列表
    """
    try:
        log_path = Path(log_dir)
        if not log_path.exists():
            return []
        
        return [str(f) for f in log_path.glob('*.log*') if f.is_file()]
        
    except Exception as e:
        logging.getLogger(__name__).error(f"获取日志文件列表失败: {str(e)}")
        return []
