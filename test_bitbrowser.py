#!/usr/bin/env python3
"""
比特浏览器连接测试脚本

测试比特浏览器API连接和窗口操作
"""

import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_bitbrowser_api():
    """测试比特浏览器API连接"""
    print("🧪 测试比特浏览器API连接")
    
    # 比特浏览器默认API地址
    api_urls = [
        "http://127.0.0.1:54345",  # 默认端口
        "http://127.0.0.1:54346",  # 备用端口
        "http://127.0.0.1:54347",  # 备用端口
    ]
    
    for api_url in api_urls:
        print(f"\n尝试连接: {api_url}")
        
        try:
            # 测试基本连接
            response = requests.get(f"{api_url}/browser/list", timeout=5)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ API连接成功!")
                
                # 获取浏览器列表
                data = response.json()
                print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if 'data' in data and isinstance(data['data'], list):
                    browsers = data['data']
                    print(f"   发现 {len(browsers)} 个浏览器窗口:")
                    
                    for i, browser in enumerate(browsers[:5]):  # 只显示前5个
                        print(f"     {i+1}. ID: {browser.get('id', 'N/A')}")
                        print(f"        名称: {browser.get('name', 'N/A')}")
                        print(f"        状态: {browser.get('status', 'N/A')}")
                        print(f"        备注: {browser.get('remark', 'N/A')}")
                
                return api_url, data
                
            else:
                print(f"   ❌ 连接失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("   ❌ 连接被拒绝 - 比特浏览器可能未启动")
        except requests.exceptions.Timeout:
            print("   ❌ 连接超时")
        except Exception as e:
            print(f"   ❌ 连接错误: {e}")
    
    return None, None

def test_browser_window(api_url, window_id):
    """测试打开指定的浏览器窗口"""
    if not api_url or not window_id:
        print("❌ 缺少API URL或窗口ID")
        return False
    
    print(f"\n🧪 测试打开浏览器窗口: {window_id}")
    
    try:
        # 准备请求数据
        json_data = {
            "id": window_id,
            "useragent": "",
            "remark": "API测试"
        }
        
        # 发送打开窗口请求
        response = requests.post(
            f"{api_url}/browser/open",
            data=json.dumps(json_data),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 窗口打开成功!")
            
            if 'data' in result:
                ws_endpoint = result['data'].get('ws', '')
                if ws_endpoint:
                    print(f"   WebSocket端点: {ws_endpoint}")
                else:
                    print("   ⚠️  未获取到WebSocket端点")
            
            return True
        else:
            print(f"   ❌ 窗口打开失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 打开窗口时出错: {e}")
        return False

def main():
    """主函数"""
    print("🎬 比特浏览器连接测试")
    print("=" * 50)
    
    # 测试API连接
    api_url, browser_data = test_bitbrowser_api()
    
    if not api_url:
        print("\n❌ 无法连接到比特浏览器API")
        print("\n💡 解决方案:")
        print("1. 确保比特浏览器已安装并启动")
        print("2. 检查比特浏览器是否开启了API服务")
        print("3. 确认API端口号是否正确（默认54345）")
        print("4. 检查防火墙设置")
        return
    
    print(f"\n✅ 成功连接到比特浏览器API: {api_url}")
    
    # 如果有浏览器窗口，测试打开第一个
    if browser_data and 'data' in browser_data and browser_data['data']:
        first_browser = browser_data['data'][0]
        window_id = first_browser.get('id')
        
        if window_id:
            print(f"\n🧪 测试打开第一个浏览器窗口...")
            success = test_browser_window(api_url, window_id)
            
            if success:
                print("\n🎉 比特浏览器连接测试完全成功!")
                print(f"✅ API地址: {api_url}")
                print(f"✅ 测试窗口ID: {window_id}")
                print("\n💡 您可以在YouTube数据抓取应用中使用这个窗口ID")
            else:
                print("\n⚠️  API连接正常，但窗口打开失败")
                print("请检查窗口ID是否正确，或窗口是否已在使用中")
        else:
            print("\n⚠️  没有可用的浏览器窗口ID")
    else:
        print("\n⚠️  没有发现可用的浏览器窗口")
        print("请在比特浏览器中创建至少一个浏览器配置")

if __name__ == "__main__":
    main()
