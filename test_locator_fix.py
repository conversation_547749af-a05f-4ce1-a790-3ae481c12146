#!/usr/bin/env python3
"""
测试 Locator API 修复
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.scrapers.youtube_scraper import YouTubeScraper
from src.core.models.profile import Profile
from src.core.browser.browser_manager import BrowserManager

async def test_locator_fix():
    """测试 Locator API 修复"""
    print("开始测试 Locator API 修复...")
    
    # 创建测试配置
    profile = Profile(
        name="test_profile",
        browser_type="chromium",
        user_data_dir="test_data",
        headless=False
    )
    
    browser_manager = BrowserManager()
    scraper = YouTubeScraper()
    
    try:
        # 启动浏览器
        print("启动浏览器...")
        browser = await browser_manager.launch_browser(profile)
        page = await browser.new_page()
        
        # 测试导航
        print("测试页面导航...")
        await scraper._navigate_to_youtube_studio(page)
        print("✓ 页面导航成功")
        
        # 测试内容菜单点击
        print("测试内容菜单点击...")
        await scraper._click_content_menu(page)
        print("✓ 内容菜单点击成功")
        
        # 测试 Shorts 标签点击
        print("测试 Shorts 标签点击...")
        await scraper._click_shorts_tab(page)
        print("✓ Shorts 标签点击成功")
        
        # 测试视频行获取
        print("测试视频行获取...")
        video_rows = await page.locator(scraper.selectors['video_rows']).all()
        print(f"✓ 成功获取 {len(video_rows)} 个视频行")
        
        # 测试第一个视频行的数据提取
        if video_rows:
            print("测试视频数据提取...")
            first_row = video_rows[0]
            video_data = await scraper._extract_video_data_from_row(first_row)
            if video_data:
                print(f"✓ 成功提取视频数据: {video_data.title}")
            else:
                print("⚠ 视频数据提取失败")
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理
        try:
            await browser.close()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_locator_fix())
