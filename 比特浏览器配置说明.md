# 比特浏览器配置说明

## 🚨 重要提醒

根据测试结果，比特浏览器API服务当前不可用。请按照以下步骤配置：

## 📋 配置步骤

### 1. 启动比特浏览器
- 确保比特浏览器客户端已安装并启动
- 比特浏览器主程序必须保持运行状态

### 2. 开启API服务
比特浏览器需要开启本地API服务才能被外部程序调用：

#### 方法一：通过设置开启
1. 打开比特浏览器客户端
2. 进入"设置"或"配置"页面
3. 找到"API服务"或"本地API"选项
4. 开启API服务
5. 确认端口号（默认通常是54345）

#### 方法二：通过命令行参数
启动比特浏览器时添加API参数：
```bash
BitBrowser.exe --enable-api --api-port=54345
```

### 3. 验证API服务
使用测试脚本验证API是否正常：
```bash
python test_bitbrowser.py
```

成功的输出应该类似：
```
✅ API连接成功!
发现 X 个浏览器窗口:
  1. ID: xxxxxxxxxx
     名称: 测试窗口
     状态: close
```

### 4. 创建浏览器配置
在比特浏览器中创建至少一个浏览器配置：
1. 点击"新建浏览器"
2. 配置基本信息（名称、备注等）
3. 保存配置
4. 记录配置的ID（用于YouTube数据抓取）

## 🔧 常见问题解决

### 问题1：连接被拒绝
**现象**：`❌ 连接被拒绝 - 比特浏览器可能未启动`

**解决方案**：
1. 确保比特浏览器主程序正在运行
2. 检查API服务是否已开启
3. 确认端口号是否正确
4. 检查防火墙是否阻止了连接

### 问题2：端口号错误
**现象**：所有端口都连接失败

**解决方案**：
1. 查看比特浏览器设置中的API端口号
2. 常见端口号：54345、54346、54347、9222
3. 修改测试脚本中的端口号进行测试

### 问题3：API服务未开启
**现象**：比特浏览器运行但API连接失败

**解决方案**：
1. 重新启动比特浏览器
2. 确保启动时开启了API服务
3. 查看比特浏览器日志是否有错误信息

## 📱 使用流程

### 配置完成后的使用步骤：

1. **启动比特浏览器**并开启API服务
2. **运行测试脚本**确认连接正常：
   ```bash
   python test_bitbrowser.py
   ```
3. **记录窗口ID**，测试脚本会显示可用的窗口ID
4. **在YouTube数据抓取应用中使用**：
   - 访问：http://127.0.0.1:8000
   - 点击"Profile管理" → "添加Profile"
   - 浏览器类型选择："bitbrowser"
   - 浏览器窗口ID填写：从测试脚本获取的ID
   - 保存Profile
5. **执行数据抓取**：
   - 点击"任务执行"
   - 选择刚创建的Profile
   - 点击"开始任务"

## ⚠️ 注意事项

1. **比特浏览器必须保持运行**：关闭比特浏览器会导致API服务停止
2. **窗口ID不能重复使用**：同一时间只能有一个程序控制一个窗口
3. **网络连接**：确保网络连接正常，能够访问YouTube
4. **账号登录**：在比特浏览器中预先登录YouTube账号
5. **权限设置**：确保YouTube账号有访问YouTube Studio的权限

## 🎯 成功标志

当一切配置正确时，您应该能看到：
- ✅ 比特浏览器API连接成功
- ✅ 能够获取到浏览器窗口列表
- ✅ 能够成功打开指定窗口
- ✅ YouTube数据抓取应用能够控制浏览器进行自动化操作

## 📞 技术支持

如果按照上述步骤仍然无法解决问题，请：
1. 检查比特浏览器版本是否支持API功能
2. 查看比特浏览器官方文档关于API的说明
3. 确认比特浏览器许可证是否支持API功能
4. 联系比特浏览器技术支持

---

**重要**：只有在比特浏览器API服务正常工作后，YouTube数据抓取功能才能正常使用。请务必先完成比特浏览器的配置！
